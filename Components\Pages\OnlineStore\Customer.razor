﻿@page "/OnlineStore"
@rendermode InteractiveServer

<h3>Customer</h3>

<MudAppBar Color="Color.Primary" Fixed="false">
    <MudIconButton Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start" />
    <MudBreadcrumbs Items="_items"></MudBreadcrumbs>
    <MudSpacer />
    <MudIconButton Icon="@Icons.Material.Filled.Store" Href= "/OnlineStore" Color="Color.Inherit" />
</MudAppBar>

<MudCard>
    <MudCardHeader>
        <CardHeaderAvatar>
            <MudAvatar Color="Color.Secondary">I</MudAvatar>
        </CardHeaderAvatar>
        <CardHeaderContent>
            <MudText Typo="Typo.body1">Sunglasses</MudText>
            <MudText Typo="Typo.body2">version 1</MudText>
        </CardHeaderContent>
        <CardHeaderActions>
            <MudIconButton Icon="@Icons.Material.Filled.Settings" Color="Color.Default" />
        </CardHeaderActions>
    </MudCardHeader>
    <MudCardMedia Image="images/Sun.jpg" Height="250" />
    <MudCardContent>
        <MudText Typo="Typo.body2">this sunglasses are made in italy</MudText>
    </MudCardContent>
    <MudCardActions>
        <MudButton Variant="Variant.Filled" EndIcon="@Icons.Material.Filled.AddShoppingCart"  Href= "/ShoppingCart" Color="Color.Primary">Add to Cart</MudButton>
        <MudIconButton Icon="@Icons.Material.Filled.Share" Color="Color.Default" />
    </MudCardActions>
</MudCard>

@code {
    private List<BreadcrumbItem> _items =
    [
        new("Home", href: "#", icon: Icons.Material.Filled.Home),
    new("Videos", href: "#", icon: Icons.Material.Filled.VideoLibrary),
    ];
}



