﻿@page "/CustomerList"
@inject NavigationManager NavigationManager
@inject ICustomerService _CustomerServices
@inject ISnackbar snackbar
@using SM.OnlineStore1.Components.Pages.CustomerComponents
@using SM.OnlineStore1.Data.Entities

<h3>قائمة العملاء</h3>

@if (_loading)
{
    <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
    <p>جاري تحميل البيانات...</p>
}
else
{
    <MudButton Color="Color.Primary"
               Variant="Variant.Filled"
               StartIcon="@Icons.Material.Filled.Add"
               @onclick="NavigateToAddCustomer"
               Class="mb-3">إضافة عميل جديد</MudButton>

    <MudSimpleTable Style="overflow-x: auto;" Hover="true" Dense="true" Striped="true">
        <thead>
            <tr>
                <th>رقم المعرف</th>
                <th>الاسم</th>
                <th>الوصف</th>
                <th>البريد الإلكتروني</th>
                <th>العمليات</th>
            </tr>
        </thead>
        <tbody>
            @if (Customers != null && Customers.Any())
            {
                @foreach (var customer in Customers)
                {
                    <tr>
                        <td>@customer.Id</td>
                        <td>@customer.Name</td>
                        <td>@customer.Description</td>
                        <td>@customer.Email</td>
                        <td>
                            <MudButton Size="Size.Small"
                                       Color="Color.Primary"
                                       Variant="Variant.Text"
                                       @onclick="()=>NavigateToAddCustomer(customer)">
                                تعديل
                            </MudButton>
                            <MudButton Size="Size.Small"
                                       Color="Color.Error"
                                       Variant="Variant.Text"
                                       @onclick="()=> DeleteCustomer(customer)">
                                حذف
                            </MudButton>
                        </td>
                    </tr>
                }
            }
            else
            {
                <tr>
                    <td colspan="5" style="text-align: center;">لا توجد بيانات</td>
                </tr>
            }
        </tbody>
    </MudSimpleTable>
}

@code {
    private bool _loading = false;
    public List<Customer> Customers { get; set; } = new List<Customer>();

    protected override async Task OnInitializedAsync()
    {
        try
        {
            _loading = true;
            Customers = await _CustomerServices.GetCustomers();
        }
        catch (Exception ex)
        {
            snackbar.Add($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", Severity.Error);
        }
        finally
        {
            _loading = false;
        }
    }

    public void NavigateToAddCustomer()
    {
        NavigationManager.NavigateTo("/CustomerEdit");
    }

    public void NavigateToAddCustomer(Customer Customer)
    {
        NavigationManager.NavigateTo($"/CustomerEdit/{Customer.Id}");
    }

    public async Task DeleteCustomer(Customer Customer)
    {
        await _CustomerServices.DeleteAsync(Customer);

        Customers.Remove(Customer);


    }
}        
