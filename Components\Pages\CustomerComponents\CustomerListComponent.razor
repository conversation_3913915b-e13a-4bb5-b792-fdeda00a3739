﻿@page "/CustomerList"
@inject NavigationManager NavigationManager
@inject ICustomerService _CustomerServices
@inject IDialogService DialogService
@inject ISnackbar snackbar
@rendermode InteractiveServer
@using SM.OnlineStore1.Components.Pages.CustomerComponents
@using SM.OnlineStore1.Data.Entities


<MudTable Items="Customers"
          Hover="true"
          Loading="@_loading"
          LoadingProgressColor="Color.Info"
          Dense="true">

    <ToolBarContent>
        <MudSpacer />
        <MudButton Color="Color.Primary"
                   Variant="Variant.Filled"
                   StartIcon="@Icons.Material.Filled.Add"
                   @onclick="NavigateToAddCustomer">إضافة</MudButton>
    </ToolBarContent>

    <HeaderContent>
        <MudTh>رقم معرف</MudTh>
        <MudTh>الإسم</MudTh>
        <MudTh>الوصف</MudTh>
        <MudTh>العمليات</MudTh>

    </HeaderContent>

    <RowTemplate>
        <MudTd DataLabel="رقم معرف">@context.Id</MudTd>
        <MudTd DataLabel="الاسم">@context.Name</MudTd>
        <MudTd DataLabel="الوصف">@context.Description</MudTd>
        <MudTd>
            <MudTooltip Text="تعديل">
                <MudIconButton Color="Color.Primary"
                               Icon="@Icons.Material.Filled.Edit"
                               OnClick="()=>NavigateToAddCustomer(context)" />
            </MudTooltip>
            <MudTooltip Text="حذف">
                <MudIconButton Color="Color.Error"
                               Icon="@Icons.Material.Filled.Delete"
                               OnClick="()=> DeleteCustomer(context)" />
            </MudTooltip>
        </MudTd>
    </RowTemplate>
</MudTable>

@code {
    private bool _loading = false;
    private bool _dataLoaded = false;
    public List<Customer> Customers { get; set; } = new List<Customer>();

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender && !_dataLoaded)
        {
            try
            {
                _loading = true;
                StateHasChanged();

                Customers = await _CustomerServices.GetCustomers();
                _dataLoaded = true;
            }
            catch (Exception ex)
            {
                snackbar.Add($"حدث خطأ أثناء تحميل البيانات: {ex.Message}", Severity.Error);
            }
            finally
            {
                _loading = false;
                StateHasChanged();
            }
        }
    }

    public void NavigateToAddCustomer()
    {
        NavigationManager.NavigateTo("/CustomerEdit");
    }

    public void NavigateToAddCustomer(Customer Customer)
    {
        NavigationManager.NavigateTo($"/CustomerEdit/{Customer.Id}");
    }

    public async Task DeleteCustomer(Customer Customer)
    {
        await _CustomerServices.DeleteAsync(Customer);

        Customers.Remove(Customer);


    }
}        
