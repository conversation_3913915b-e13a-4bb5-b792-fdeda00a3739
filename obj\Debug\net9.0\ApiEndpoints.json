[{"ContainingType": "Microsoft.AspNetCore.Routing.IdentityComponentsEndpointRouteBuilderExtensions+<>c", "Method": "<MapAdditionalIdentityEndpoints>b__0_1", "RelativePath": "Account/Logout", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "returnUrl", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Microsoft.AspNetCore.Routing.IdentityComponentsEndpointRouteBuilderExtensions+<>c__DisplayClass0_0", "Method": "<MapAdditionalIdentityEndpoints>b__3", "RelativePath": "Account/Manage/DownloadPersonalData", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Microsoft.AspNetCore.Routing.IdentityComponentsEndpointRouteBuilderExtensions+<>c", "Method": "<MapAdditionalIdentityEndpoints>b__0_2", "RelativePath": "Account/Manage/LinkExternalLogin", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "provider", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Microsoft.AspNetCore.Routing.IdentityComponentsEndpointRouteBuilderExtensions+<>c", "Method": "<MapAdditionalIdentityEndpoints>b__0_0", "RelativePath": "Account/PerformExternalLogin", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "provider", "Type": "System.String", "IsRequired": true}, {"Name": "returnUrl", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}]