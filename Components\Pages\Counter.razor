﻿@page "/counter"
@rendermode InteractiveServer
@inject ISnackbar Snackbar


<PageTitle>Counter</PageTitle>

<MudText Typo="Typo.h3" GutterBottom="true">Counter</MudText>

<MudText Typo="Typo.body1" Class="mb-4">Current count: @currentCount</MudText>

<MudButton Color="Color.Primary" Variant="Variant.Filled" @onclick="IncrementCount">Click me</MudButton>
<MudButton @onclick="@(() => Snackbar.Add("My Close button is gone!", Severity.Normal, config => { config.ShowCloseIcon = false; }))" Variant="Variant.Filled" Color="Color.Primary">
    Open Modified Snackbar
</MudButton>
@code {
    private int currentCount = 0;

    private void IncrementCount()
    {
        currentCount++;
    }
}