﻿using Microsoft.EntityFrameworkCore;
using OnlineStore1.Data;
using SM.OnlineStore1.Data.Entities;

namespace SM.OnlineStore1.Components.Pages.CustomerComponents
{
    public class CustomerService : ICustomerService
    {

        private readonly IDbContextFactory<ApplicationDbContext>? _dbContextFactory;
        public CustomerService(IDbContextFactory<ApplicationDbContext>? dbContextFactory)
        {
            _dbContextFactory = dbContextFactory;
        }
        public async Task DeleteAsync(Customer Customer)
        {
            var _dbContext = _dbContextFactory.CreateDbContext();

            var existingCustomer = _dbContext.Customer
                .Find(Customer.Id);
            if (existingCustomer != null)
            {
                _dbContext.Customer.Remove(existingCustomer);
                await _dbContext.SaveChangesAsync();
            }
        }
        public Task<Customer?> GetCustomerById(Guid id)
        {
            var _dbContext = _dbContextFactory.CreateDbContext();
            return _dbContext.Customer
                .FirstOrDefaultAsync(a => a.Id == id);
        }
        public async Task<Customer> Save(Customer Customer)
        {
            var db = _dbContextFactory.CreateDbContext();
            db.Customer.Add(Customer);
            await db.SaveChangesAsync();
            return Customer;
        }
        public Task<Customer> Update(Customer Customer)
        {
            throw new NotImplementedException();
        }
        public async Task<Customer> Upsert(Customer Customer)
        {
            var _dbContext = _dbContextFactory?.CreateDbContext();

            var existingCustomer = await _dbContext.Customer
                .FirstOrDefaultAsync(a => a.Id == Customer.Id);

            if (existingCustomer != null)
            {
                // Update existing Customer
                existingCustomer.Name = Customer.Name;
                existingCustomer.Description = Customer.Description;
                _dbContext.Customer.Update(existingCustomer);
            }
            else
            {
                // Add new Customer
                await _dbContext.Customer.AddAsync(Customer);
            }
            await _dbContext.SaveChangesAsync();
            return Customer;
        }
        public async Task<List<Customer>> GetCustomers()
        {
            using var dbContext = _dbContextFactory.CreateDbContext();
            return await dbContext.Customer.ToListAsync();
        }

    }
}