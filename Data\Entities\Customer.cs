using System.ComponentModel.DataAnnotations;

namespace SM.OnlineStore1.Data.Entities
{
    public class Customer
    {
        [Key]
        public Guid Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public int BankNum { get; set; }
        public int CreditCard { get; set; }
        public string Address { get; set; } = string.Empty;

        public List<Order> Orders { get; set; } = [];

    }




}

