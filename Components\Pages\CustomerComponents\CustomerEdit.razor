﻿@page "/CustomerEdit"
@page "/CustomerEdit/{Id:guid}"
@using SM.OnlineStore1.Components.Pages.CustomerComponents
@using SM.OnlineStore1.Data.Entities
@inject ICustomerService CustomerService
@rendermode InteractiveServer
@inject NavigationManager NavigationManager
@inject ISnackbar Snackbar

@if (Customer == null)
{
    <div class="d-flex justify-center align-center ma-auto">
        <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" />
    </div>
}
else
{

    <MudForm Model="Customer" @ref="form">
        <MudCard Elevation="6">
            <MudCardHeader>
                <MudText Typo="Typo.h5" Align="Align.Center">@PageHeaderText</MudText>
            </MudCardHeader>
            <MudItem xs="12" md="12" lg="12">
                <MudTextField @bind-Value="Customer.Name"
                              Label="الإسم"
                              Variant="Variant.Outlined"
                              Margin="Margin.Dense" />
            </MudItem>
            <MudItem xs="12" md="12" lg="12">
                <MudTextField @bind-Value="Customer.Description"
                              Label="الوصف"
                              Variant="Variant.Outlined"
                              Margin="Margin.Dense" />
            </MudItem>

            <MudCardActions Class="d-flex justify-end mt-2">
                <MudButton Variant="Variant.Outlined"
                           Color="Color.Primary"
                           OnClick="HandleSaveAsync">حفظ</MudButton>
                <MudButton Variant="Variant.Filled"
                           Color="Color.Primary"
                           OnClick="HandleSaveAndCloseAsync">حفظ و إغلاق</MudButton>
            </MudCardActions>
        </MudCard>
    </MudForm>
}

@code {
    protected MudForm? form;
    protected bool success = false;
    private string PageHeaderText = "إضافة إدارة جديدة";

    [Parameter] public Guid Id { get; set; }

    private Customer? Customer { get; set; } = new Customer();


    protected override async Task OnInitializedAsync()
    {
        if (Id == Guid.Empty)
        {
            PageHeaderText = "إضافة إدارة جديدة";
            Customer = new Customer();
        }
        else
        {
            PageHeaderText = "تعديل إدارة";
            Customer = await CustomerService.GetCustomerById(Id);
        }
    }



    private async Task HandleSaveAsync()
    {
        if (Customer != null)
        {
            await CustomerService.Upsert(Customer);
            Snackbar.Configuration.PositionClass = Defaults.Classes.Position.TopCenter;
            Snackbar.Add("تم الحفظ بنجاح", Severity.Success);
        }
    }

    private async Task HandleSaveAndCloseAsync()
    {
        if (Customer != null)
        {
            await CustomerService.Upsert(Customer);
            NavigationManager.NavigateTo("/CustomerList", true);
        }
    }

}