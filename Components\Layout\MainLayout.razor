﻿@inherits LayoutComponentBase

<MudRTLProvider RightToLeft="true">
    <MudThemeProvider />
    <MudPopoverProvider />
    <MudDialogProvider />
    <MudSnackbarProvider />

    <MudLayout>
        <MudAppBar Elevation="1">
            <MudStaticNavDrawerToggle DrawerId="nav-drawer" Icon="@Icons.Material.Filled.Menu" Color="Color.Inherit" Edge="Edge.Start" />
            <MudText Typo="Typo.h5" Class="ml-3">OnlineStore</MudText>
        </MudAppBar>
        <MudDrawer id="nav-drawer" @bind-Open="_drawerOpen" ClipMode="DrawerClipMode.Always" Elevation="2">
            <NavMenu />
        </MudDrawer>
        <MudMainContent Class="pt-16 pa-4">
            @Body
        </MudMainContent>
    </MudLayout>
</MudRTLProvider>

<div id="blazor-error-ui" data-nosnippet>
    An unhandled error has occurred.
    <a href="." class="reload">Reload</a>
    <span class="dismiss">🗙</span>
</div>

@code {
    private bool _drawerOpen = true;
}
