{"Version": 1, "ManifestType": "Build", "Endpoints": [{"Route": "_content/Extensions.MudBlazor.StaticInput/NavigationObserver.js", "AssetFile": "_content/Extensions.MudBlazor.StaticInput/NavigationObserver.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "6859"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"5xAqVXGu6Tv7zFwKN73F345Yp8b+mUnkI3nYcD6QCXg=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Feb 2025 14:29:49 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5xAqVXGu6Tv7zFwKN73F345Yp8b+mUnkI3nYcD6QCXg="}]}, {"Route": "_content/Extensions.MudBlazor.StaticInput/NavigationObserver.js", "AssetFile": "_content/Extensions.MudBlazor.StaticInput/NavigationObserver.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000665335995"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1502"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QmEcSw7onHqXA8zTTGusAX2ZXAW6caknC3+uUNsCUos=\""}, {"Name": "ETag", "Value": "W/\"5xAqVXGu6Tv7zFwKN73F345Yp8b+mUnkI3nYcD6QCXg=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 09:19:22 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5xAqVXGu6Tv7zFwKN73F345Yp8b+mUnkI3nYcD6QCXg="}]}, {"Route": "_content/Extensions.MudBlazor.StaticInput/NavigationObserver.js.gz", "AssetFile": "_content/Extensions.MudBlazor.StaticInput/NavigationObserver.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "1502"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QmEcSw7onHqXA8zTTGusAX2ZXAW6caknC3+uUNsCUos=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 09:19:22 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QmEcSw7onHqXA8zTTGusAX2ZXAW6caknC3+uUNsCUos="}]}, {"Route": "_content/Extensions.MudBlazor.StaticInput/NavigationObserver.lp4d2hvui5.js", "AssetFile": "_content/Extensions.MudBlazor.StaticInput/NavigationObserver.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "6859"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"5xAqVXGu6Tv7zFwKN73F345Yp8b+mUnkI3nYcD6QCXg=\""}, {"Name": "Last-Modified", "Value": "Thu, 20 Feb 2025 14:29:49 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lp4d2hvui5"}, {"Name": "integrity", "Value": "sha256-5xAqVXGu6Tv7zFwKN73F345Yp8b+mUnkI3nYcD6QCXg="}, {"Name": "label", "Value": "_content/Extensions.MudBlazor.StaticInput/NavigationObserver.js"}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css", "AssetFile": "_content/MudBlazor/MudBlazor.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "604382"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dbrztyw7cpD/dvGeaqylyRiNofbn9I08NpD/6iCdbjA=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 08:29:34 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dbrztyw7cpD/dvGeaqylyRiNofbn9I08NpD/6iCdbjA="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css", "AssetFile": "_content/MudBlazor/MudBlazor.min.css.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000015306435"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "65331"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"77tpU9AWy7RQ4BEik1v/gTsvtRn02mER5xv3B2/FG/k=\""}, {"Name": "ETag", "Value": "W/\"dbrztyw7cpD/dvGeaqylyRiNofbn9I08NpD/6iCdbjA=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 09:19:22 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dbrztyw7cpD/dvGeaqylyRiNofbn9I08NpD/6iCdbjA="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.css.gz", "AssetFile": "_content/MudBlazor/MudBlazor.min.css.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "65331"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"77tpU9AWy7RQ4BEik1v/gTsvtRn02mER5xv3B2/FG/k=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 09:19:22 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-77tpU9AWy7RQ4BEik1v/gTsvtRn02mER5xv3B2/FG/k="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.f7451o6lak.css", "AssetFile": "_content/MudBlazor/MudBlazor.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "604382"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"dbrztyw7cpD/dvGeaqylyRiNofbn9I08NpD/6iCdbjA=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 08:29:34 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "f7451o6lak"}, {"Name": "integrity", "Value": "sha256-dbrztyw7cpD/dvGeaqylyRiNofbn9I08NpD/6iCdbjA="}, {"Name": "label", "Value": "_content/MudBlazor/MudBlazor.min.css"}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js", "AssetFile": "_content/MudBlazor/MudBlazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "73018"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gCxH2RI5xXVkxLFgcHgDuyoXu7IWZwdvZ5USM+27dIk=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 08:29:34 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gCxH2RI5xXVkxLFgcHgDuyoXu7IWZwdvZ5USM+27dIk="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js", "AssetFile": "_content/MudBlazor/MudBlazor.min.js.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000065184799"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15340"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+exlo59CKmCfU04eDbiDbKqeJaYCIVvhg64jqp35nG8=\""}, {"Name": "ETag", "Value": "W/\"gCxH2RI5xXVkxLFgcHgDuyoXu7IWZwdvZ5USM+27dIk=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 09:19:22 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gCxH2RI5xXVkxLFgcHgDuyoXu7IWZwdvZ5USM+27dIk="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.js.gz", "AssetFile": "_content/MudBlazor/MudBlazor.min.js.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "15340"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+exlo59CKmCfU04eDbiDbKqeJaYCIVvhg64jqp35nG8=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 09:19:22 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+exlo59CKmCfU04eDbiDbKqeJaYCIVvhg64jqp35nG8="}]}, {"Route": "_content/MudBlazor/MudBlazor.min.shgs22i5w1.js", "AssetFile": "_content/MudBlazor/MudBlazor.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "73018"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"gCxH2RI5xXVkxLFgcHgDuyoXu7IWZwdvZ5USM+27dIk=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 08:29:34 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "shgs22i5w1"}, {"Name": "integrity", "Value": "sha256-gCxH2RI5xXVkxLFgcHgDuyoXu7IWZwdvZ5USM+27dIk="}, {"Name": "label", "Value": "_content/MudBlazor/MudBlazor.min.js"}]}, {"Route": "favicon.2jeq8efc6q.ico", "AssetFile": "favicon.ico.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000336021505"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2975"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"b7CPHqpoIGsGVgOrEO+r2XPyaLrLUBwkA6R2jOMbS7M=\""}, {"Name": "ETag", "Value": "W/\"8kNQh+LErZHx3sMz237BHWFasAGQ88EWakJrWWYOxTA=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 09:19:22 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2jeq8efc6q"}, {"Name": "integrity", "Value": "sha256-8kNQh+LErZHx3sMz237BHWFasAGQ88EWakJrWWYOxTA="}, {"Name": "label", "Value": "favicon.ico"}]}, {"Route": "favicon.2jeq8efc6q.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Length", "Value": "15086"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"8kNQh+LErZHx3sMz237BHWFasAGQ88EWakJrWWYOxTA=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 17:45:33 GMT"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2jeq8efc6q"}, {"Name": "integrity", "Value": "sha256-8kNQh+LErZHx3sMz237BHWFasAGQ88EWakJrWWYOxTA="}, {"Name": "label", "Value": "favicon.ico"}]}, {"Route": "favicon.2jeq8efc6q.ico.gz", "AssetFile": "favicon.ico.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2975"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"b7CPHqpoIGsGVgOrEO+r2XPyaLrLUBwkA6R2jOMbS7M=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 09:19:22 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2jeq8efc6q"}, {"Name": "integrity", "Value": "sha256-b7CPHqpoIGsGVgOrEO+r2XPyaLrLUBwkA6R2jOMbS7M="}, {"Name": "label", "Value": "favicon.ico.gz"}]}, {"Route": "favicon.ico", "AssetFile": "favicon.ico.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000336021505"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2975"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"b7CPHqpoIGsGVgOrEO+r2XPyaLrLUBwkA6R2jOMbS7M=\""}, {"Name": "ETag", "Value": "W/\"8kNQh+LErZHx3sMz237BHWFasAGQ88EWakJrWWYOxTA=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 09:19:22 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8kNQh+LErZHx3sMz237BHWFasAGQ88EWakJrWWYOxTA="}]}, {"Route": "favicon.ico", "AssetFile": "favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Length", "Value": "15086"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"8kNQh+LErZHx3sMz237BHWFasAGQ88EWakJrWWYOxTA=\""}, {"Name": "Last-Modified", "Value": "Fri, 30 May 2025 17:45:33 GMT"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8kNQh+LErZHx3sMz237BHWFasAGQ88EWakJrWWYOxTA="}]}, {"Route": "favicon.ico.gz", "AssetFile": "favicon.ico.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Content-Length", "Value": "2975"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"b7CPHqpoIGsGVgOrEO+r2XPyaLrLUBwkA6R2jOMbS7M=\""}, {"Name": "Last-Modified", "Value": "Sat, 31 May 2025 09:19:22 GMT"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b7CPHqpoIGsGVgOrEO+r2XPyaLrLUBwkA6R2jOMbS7M="}]}]}