using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using SM.OnlineStore1.Data.Entities;

namespace OnlineStore1.Data
{
    public class ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : IdentityDbContext<ApplicationUser>(options)
    {

            public DbSet<Order> Order { get; set; } = default!;
            public DbSet<Customer> Customer { get; set; } = default!;

    }
}
