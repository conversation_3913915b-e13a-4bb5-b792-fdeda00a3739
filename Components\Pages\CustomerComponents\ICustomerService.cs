﻿using OnlineStore1.Data;
using SM.OnlineStore1.Data.Entities;

namespace SM.OnlineStore1.Components.Pages.CustomerComponents
{
    public interface ICustomerService
    {
        Task DeleteAsync(Customer Customer);
        Task<Customer?> GetCustomerById(Guid id);
        Task<List<Customer>> GetCustomers();
        Task<Customer> Save(Customer Customer);
        Task<Customer> Update(Customer Customer);
        Task<Customer> Upsert(Customer Customer);
    }
}